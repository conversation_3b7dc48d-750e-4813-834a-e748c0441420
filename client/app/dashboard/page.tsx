"use client"

import React, { useState, useEffect, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  ArrowRight,
  Plus,
  RefreshCw,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle,
  Eye,
  Calendar,
  DollarSign,
  Building,
  ChevronLeft,
  DoorClosedIcon,
  DoorOpenIcon,
  Filter,
  SortDesc,
  Search,
  FileText,
  Upload,
  CheckCircle2,
} from "lucide-react"
import Image from "next/image"

// Types
interface RentalAdvanceRequest {
  id: string
  rentAmount: number
  monthsToAdvance: number
  currentStatus: string
  statusLabel: string
  proposalAmount?: number
  monthlyRentOffer?: number
  proposedMonths?: number
  realEstate?: {
    name: string
    cnpj: string
  }
  createdAt: string
  updatedAt: string
  canEdit: boolean
  canCancel: boolean
  nextSteps: string[]
}

interface FilterOptions {
  status: string
  sortBy: 'updatedAt' | 'createdAt'
  sortOrder: 'desc' | 'asc'
  searchTerm: string
}

interface DashboardState {
  requests: RentalAdvanceRequest[]
  loading: boolean
  error: string
  user: any | null
  authToken: string | null
  filters: FilterOptions
}

export default function DashboardPage() {
  const [state, setState] = useState<DashboardState>({
    requests: [],
    loading: true,
    error: "",
    user: null,
    authToken: null,
    filters: {
      status: 'all',
      sortBy: 'updatedAt',
      sortOrder: 'desc',
      searchTerm: '',
    },
  })

  // Load user data from localStorage on component mount
  useEffect(() => {
    try {
      const savedState = localStorage.getItem("x-auth-state")
      if (savedState) {
        const parsed = JSON.parse(savedState)
        if (parsed.user && parsed.authToken) {
          setState(prev => ({
            ...prev,
            user: parsed.user,
            authToken: parsed.authToken,
          }))
        } else {
          // Redirect to login if no auth data
          window.location.href = "/"
        }
      } else {
        // Redirect to login if no saved state
        window.location.href = "/"
      }
    } catch (error) {
      console.error("Error loading user data:", error)
      window.location.href = "/"
    }
  }, [])

  // Fetch rental advance requests
  const fetchRequests = useCallback(async () => {
    if (!state.authToken) return

    setState(prev => ({ ...prev, loading: true, error: "" }))

    try {
      const response = await fetch("/api/v1/rental-advance", {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${state.authToken}`,
        },
      })

      const result = await response.json()

      if (response.ok) {
        setState(prev => ({
          ...prev,
          requests: result.data || [],
          loading: false,
        }))
      } else {
        setState(prev => ({
          ...prev,
          error: result.message || "Erro ao carregar solicitações",
          loading: false,
        }))
      }
    } catch (error) {
      console.error("Error fetching requests:", error)
      setState(prev => ({
        ...prev,
        error: "Erro de conexão. Tente novamente.",
        loading: false,
      }))
    }
  }, [state.authToken])

  // Fetch requests when authToken is available
  useEffect(() => {
    if (state.authToken) {
      fetchRequests()
    }
  }, [state.authToken, fetchRequests])

  // Helper functions
  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Determine if a request requires user action
  const requiresUserAction = (status: string): boolean => {
    const actionRequiredStatuses = [
      'pdf_extracted',      // Validation step - user needs to confirm extracted data
      'proposal_sent',      // Proposal step - user needs to accept/reject proposal
      'docs_uploaded',      // Document upload step - user needs to upload identity documents (after proposal acceptance)
    ]
    return actionRequiredStatuses.includes(status)
  }

  // Get the appropriate route for mid-flow entry
  const getMidFlowRoute = (request: RentalAdvanceRequest): string | null => {
    switch (request.currentStatus) {
      case 'pdf_extracted':
        return `/anticipation/validation/${request.id}`
      case 'proposal_sent':
        // Check if proposal was rejected (indicated by -1 values)
        if (request.proposalAmount === -1) {
          return null // No action needed for rejected proposals
        }
        return `/anticipation/proposal/${request.id}`
      case 'docs_uploaded':
        return `/anticipation/confirmation/${request.id}`
      default:
        return null
    }
  }

  // Filter and sort requests
  const getFilteredAndSortedRequests = () => {
    let filtered = [...state.requests]

    // Apply status filter
    if (state.filters.status !== 'all') {
      filtered = filtered.filter(request => request.currentStatus === state.filters.status)
    }

    // Apply search filter
    if (state.filters.searchTerm) {
      const searchLower = state.filters.searchTerm.toLowerCase()
      filtered = filtered.filter(request =>
        request.id.toLowerCase().includes(searchLower) ||
        request.statusLabel.toLowerCase().includes(searchLower) ||
        request.realEstate?.name.toLowerCase().includes(searchLower)
      )
    }

    // Sort requests
    filtered.sort((a, b) => {
      const aDate = new Date(state.filters.sortBy === 'updatedAt' ? a.updatedAt : a.createdAt)
      const bDate = new Date(state.filters.sortBy === 'updatedAt' ? b.updatedAt : b.createdAt)

      if (state.filters.sortOrder === 'desc') {
        return bDate.getTime() - aDate.getTime()
      } else {
        return aDate.getTime() - bDate.getTime()
      }
    })

    return filtered
  }

  // Categorize requests
  const categorizeRequests = () => {
    const filtered = getFilteredAndSortedRequests()

    const actionRequired = filtered.filter(request => requiresUserAction(request.currentStatus))
    const inProgress = filtered.filter(request => !requiresUserAction(request.currentStatus))

    return { actionRequired, inProgress }
  }

  // Get status icon and color
  const getStatusDisplay = (status: string) => {
    const statusMap: Record<string, { icon: React.ReactNode; color: string; bg: string }> = {
      created: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      pdf_uploaded: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      pdf_extracted: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      data_confirmed: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      pending_proposal: { icon: <Clock className="w-4 h-4" />, color: "text-yellow-600", bg: "bg-yellow-50" },
      proposal_sent: { icon: <AlertCircle className="w-4 h-4" />, color: "text-orange-600", bg: "bg-orange-50" },
      docs_uploaded: { icon: <Clock className="w-4 h-4" />, color: "text-blue-600", bg: "bg-blue-50" },
      awaiting_review: { icon: <Clock className="w-4 h-4" />, color: "text-purple-600", bg: "bg-purple-50" },
      approved: { icon: <CheckCircle className="w-4 h-4" />, color: "text-green-600", bg: "bg-green-50" },
      rejected: { icon: <XCircle className="w-4 h-4" />, color: "text-red-600", bg: "bg-red-50" },
      cancelled: { icon: <XCircle className="w-4 h-4" />, color: "text-gray-600", bg: "bg-gray-50" },
    }
    return statusMap[status] || statusMap.created
  }

  // Filter update functions
  const updateFilter = (key: keyof FilterOptions, value: string) => {
    setState(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: value,
      },
    }))
  }

  const resetFilters = () => {
    setState(prev => ({
      ...prev,
      filters: {
        status: 'all',
        sortBy: 'updatedAt',
        sortOrder: 'desc',
        searchTerm: '',
      },
    }))
  }

  // Handle new request
  const handleNewRequest = () => {
    window.location.href = "/anticipation"
  }

  // Handle logout
  const handleLogout = () => {
    localStorage.removeItem("x-auth-state")
    window.location.href = "/"
  }

  // Handle mid-flow entry
  const handleMidFlowEntry = (request: RentalAdvanceRequest) => {
    const route = getMidFlowRoute(request)
    if (route) {
      window.location.href = route
    }
  }

  // Style classes (matching page.tsx)
  const mainButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-[#0B4375] to-blue-700 hover:from-[#0B4375] hover:to-blue-800 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"
  const greenButtonClass =
    "w-full h-12 text-sm font-semibold bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white transition-all duration-200 rounded-lg shadow-md hover:shadow-lg flex items-center justify-center gap-2"

  if (state.loading && !state.requests.length) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375] flex items-center justify-center">
        <div className="text-center p-8 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl max-w-sm mx-4 border border-white/20">
          <div className="relative mb-8">
            <div className="w-20 h-20 border-4 border-gray-200 rounded-full animate-spin mx-auto">
              <div className="w-full h-full border-4 border-transparent border-t-blue-600 border-r-blue-700 rounded-full animate-spin"></div>
            </div>
          </div>
          <p className="text-[#0B4375] font-bold text-lg">Carregando suas solicitações...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0B4375] to-[#0B4375]">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 px-4 lg:px-8 pt-6">
        <div className="w-10 h-10 lg:w-auto">
          <div className="hidden lg:flex items-center gap-3">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-2 border border-white/20">
              <Image src="/images/locpay-logo.png" alt="LocPay" width={120} height={32} className="h-8 w-auto" />
            </div>
            <div className="text-white">
              <h1 className="text-xl font-bold">Dashboard</h1>
              <p className="text-sm text-white/80">Antecipações de Aluguel</p>
            </div>
          </div>
        </div>
        <div className="flex justify-center lg:hidden">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3 border border-white/20 shadow-lg">
            <Image src="/images/locpay-logo.png" alt="LocPay" width={100} height={26} className="h-6 w-auto" />
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="hidden lg:block text-right text-white">
            <p className="text-sm font-medium">{state.user?.name || "Usuário"}</p>
            <p className="text-xs text-white/70">{state.user?.cpf || ""}</p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleLogout}
            className="text-white hover:bg-white/20 w-10 h-10 rounded-lg transition-all duration-200"
            aria-label="Sair"
          >
            <DoorOpenIcon className="h-5 w-5" />
          </Button>
        </div>
      </div>

      <div className="px-4 lg:px-8 pb-8 max-w-7xl mx-auto">
        {/* Welcome Section */}
        <div className="text-center lg:text-left mb-6 lg:mb-8">
          <div className="lg:flex lg:items-center lg:justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-white mb-2">
                <span className="lg:hidden">Olá, {state.user?.name?.split(" ")[0] || "Usuário"}!</span>
                <span className="hidden lg:inline">Suas Solicitações</span>
              </h1>
              <p className="text-white/90 text-sm lg:text-base">
                <span className="lg:hidden">Suas solicitações de antecipação de aluguel</span>
                <span className="hidden lg:inline">Gerencie suas antecipações de aluguel de forma simples e rápida</span>
              </p>
            </div>
            <div className="hidden lg:flex items-center gap-4 mt-4 lg:mt-0">
              <div className="text-right text-white/80 text-sm">
                <p>Total de solicitações</p>
                <p className="text-2xl font-bold text-white">{state.requests.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Bar */}
        <div className="flex flex-col lg:flex-row gap-4 mb-6 lg:mb-8">
          {/* New Request Button */}
          <div className="lg:w-auto">
            <Button onClick={handleNewRequest} className={`${greenButtonClass} lg:h-14 lg:px-8 lg:text-base`}>
              <Plus className="w-4 h-4 lg:w-5 lg:h-5" />
              Nova Solicitação
              <ArrowRight className="w-4 h-4 lg:w-5 lg:h-5" />
            </Button>
          </div>

          {/* Filters and Search */}
          <div className="flex-1 lg:flex lg:items-center lg:justify-end lg:gap-6">
            {/* Search */}
            <div className="relative mb-3 lg:mb-0 lg:w-80">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-4 h-4 lg:w-5 lg:h-5" />
              <input
                type="text"
                placeholder="Buscar por ID, status ou imobiliária..."
                value={state.filters.searchTerm}
                onChange={(e) => updateFilter('searchTerm', e.target.value)}
                className="w-full h-10 lg:h-12 pl-10 lg:pl-12 pr-4 bg-white/10 border border-white/20 rounded-lg text-white placeholder:text-white/60 focus:bg-white/20 focus:border-white/40 transition-all duration-200 lg:text-base"
              />
            </div>

            {/* Filter and Sort Controls */}
            <div className="flex gap-2 lg:gap-3">
              <select
                value={state.filters.status}
                onChange={(e) => updateFilter('status', e.target.value)}
                className="h-10 lg:h-12 px-3 lg:px-4 bg-white/10 border border-white/20 rounded-lg text-white text-sm lg:text-base focus:bg-white/20 focus:border-white/40 transition-all duration-200 min-w-[140px] lg:min-w-[180px]"
              >
                <option value="all" className="text-gray-900">Todos os Status</option>
                <option value="pdf_extracted" className="text-gray-900">Aguardando Validação</option>
                <option value="proposal_sent" className="text-gray-900">Proposta Enviada</option>
                <option value="docs_uploaded" className="text-gray-900">Documentos Enviados</option>
                <option value="approved" className="text-gray-900">Aprovado</option>
                <option value="rejected" className="text-gray-900">Rejeitado</option>
              </select>

              <select
                value={`${state.filters.sortBy}-${state.filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split('-') as [typeof state.filters.sortBy, typeof state.filters.sortOrder]
                  updateFilter('sortBy', sortBy)
                  updateFilter('sortOrder', sortOrder)
                }}
                className="h-10 lg:h-12 px-3 lg:px-4 bg-white/10 border border-white/20 rounded-lg text-white text-sm lg:text-base focus:bg-white/20 focus:border-white/40 transition-all duration-200 min-w-[120px] lg:min-w-[160px]"
              >
                <option value="updatedAt-desc" className="text-gray-900">Mais Recentes</option>
                <option value="updatedAt-asc" className="text-gray-900">Mais Antigos</option>
                <option value="createdAt-desc" className="text-gray-900">Criação Recente</option>
                <option value="createdAt-asc" className="text-gray-900">Criação Antiga</option>
              </select>

              <Button
                onClick={resetFilters}
                variant="ghost"
                size="icon"
                className="h-10 lg:h-12 w-10 lg:w-12 text-white hover:bg-white/20 border border-white/20"
              >
                <RefreshCw className="w-4 h-4 lg:w-5 lg:h-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {state.error && (
          <Card className="bg-red-50 border-red-200 mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{state.error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Requests List */}
        {state.requests.length === 0 && !state.loading ? (
          <Card className="bg-white shadow-xl rounded-2xl border-0">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Building className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">Nenhuma solicitação encontrada</h3>
              <p className="text-gray-600 text-sm mb-6">
                Você ainda não possui solicitações de antecipação de aluguel.
              </p>
              <Button onClick={handleNewRequest} className={mainButtonClass}>
                <Plus className="w-4 h-4" />
                Fazer Primeira Solicitação
              </Button>
            </CardContent>
          </Card>
        ) : (
          (() => {
            const { actionRequired, inProgress } = categorizeRequests()

            return (
              <div className="space-y-8">
                {/* Action Required Section */}
                {actionRequired.length > 0 && (
                  <div>
                    <Card className="bg-gradient-to-r from-orange-50 to-red-50 shadow-xl rounded-2xl border-0 border-l-4 border-l-orange-500">
                      <CardHeader className="pb-3 lg:pb-4">
                        <CardTitle className="flex items-center gap-2 text-lg lg:text-xl text-orange-700">
                          <div className="p-2 bg-orange-100 rounded-full">
                            <AlertCircle className="w-5 h-5 lg:w-6 lg:h-6 text-orange-600" />
                          </div>
                          Ação Necessária ({actionRequired.length})
                          <div className="ml-auto">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 animate-pulse">
                              Requer Atenção
                            </span>
                          </div>
                        </CardTitle>
                        <p className="text-sm lg:text-base text-gray-700">
                          Solicitações que precisam da sua atenção para prosseguir
                        </p>
                      </CardHeader>
                    </Card>

                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 mt-4 lg:mt-6">
                      {actionRequired.map((request) => {
                        const statusDisplay = getStatusDisplay(request.currentStatus)
                        const midFlowRoute = getMidFlowRoute(request)
                        const isRejectedProposal = request.currentStatus === 'proposal_sent' && request.proposalAmount === -1

                        return (
                          <Card
                            key={request.id}
                            className="bg-gradient-to-br from-white to-orange-50/30 shadow-xl rounded-2xl border-0 border-l-4 border-l-orange-400 hover:shadow-2xl hover:border-l-orange-500 transition-all duration-200 lg:h-full relative overflow-hidden"
                          >
                            {/* Action Required Indicator */}
                            <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-orange-400"></div>
                            <div className="absolute top-1 right-1 w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                            <CardContent className="p-6 lg:p-8 h-full flex flex-col">
                              <div className="flex items-start justify-between mb-4 lg:mb-6">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-2">
                                    <div className={`p-2 lg:p-3 rounded-lg ${statusDisplay.bg}`}>
                                      <div className={statusDisplay.color}>{statusDisplay.icon}</div>
                                    </div>
                                    <div>
                                      <h3 className="font-semibold lg:text-lg text-gray-800">{request.statusLabel}</h3>
                                      <p className="text-xs lg:text-sm text-gray-500">ID: {request.id.slice(-8)}</p>
                                    </div>
                                  </div>
                                </div>
                                <div className="flex flex-col lg:flex-row gap-2">
                                  {midFlowRoute && !isRejectedProposal && (
                                    <Button
                                      onClick={() => handleMidFlowEntry(request)}
                                      size="sm"
                                      className="bg-orange-500 hover:bg-orange-600 text-white lg:h-10 lg:px-4"
                                    >
                                      {request.currentStatus === 'pdf_extracted' && (
                                        <>
                                          <FileText className="w-4 h-4 mr-1 lg:mr-2" />
                                          <span className="hidden lg:inline">Validar Dados</span>
                                          <span className="lg:hidden">Validar</span>
                                        </>
                                      )}
                                      {request.currentStatus === 'proposal_sent' && (
                                        <>
                                          <CheckCircle2 className="w-4 h-4 mr-1 lg:mr-2" />
                                          <span className="hidden lg:inline">Ver Proposta</span>
                                          <span className="lg:hidden">Proposta</span>
                                        </>
                                      )}
                                      {request.currentStatus === 'docs_uploaded' && (
                                        <>
                                          <Upload className="w-4 h-4 mr-1 lg:mr-2" />
                                          <span className="hidden lg:inline">Enviar Documentos</span>
                                          <span className="lg:hidden">Documentos</span>
                                        </>
                                      )}
                                    </Button>
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="text-[#0B4375] hover:bg-blue-50 lg:h-10 lg:w-10"
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </div>

                              <div className="grid grid-cols-2 gap-4 mb-4">
                                <div className="flex items-center gap-2">
                                  <DollarSign className="w-4 h-4 text-gray-400" />
                                  <div>
                                    <p className="text-xs text-gray-500">Valor Mensal</p>
                                    <p className="font-semibold text-gray-800">{formatCurrency(request.rentAmount)}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Calendar className="w-4 h-4 text-gray-400" />
                                  <div>
                                    <p className="text-xs text-gray-500">Meses</p>
                                    <p className="font-semibold text-gray-800">{request.monthsToAdvance}</p>
                                  </div>
                                </div>
                              </div>

                              {request.realEstate && (
                                <div className="flex items-center gap-2 mb-4">
                                  <Building className="w-4 h-4 text-gray-400" />
                                  <div>
                                    <p className="text-xs text-gray-500">Imobiliária</p>
                                    <p className="text-sm text-gray-700">{request.realEstate.name}</p>
                                  </div>
                                </div>
                              )}

                              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4 text-gray-400" />
                                  <span className="text-xs text-gray-500">
                                    Atualizado em {formatDateTime(request.updatedAt)}
                                  </span>
                                </div>
                                {request.proposalAmount && request.proposalAmount !== -1 && (
                                  <div className="text-right">
                                    <p className="text-xs text-gray-500">Proposta</p>
                                    <p className="font-semibold text-green-600">{formatCurrency(request.proposalAmount)}</p>
                                  </div>
                                )}
                                {isRejectedProposal && (
                                  <div className="text-right">
                                    <p className="text-xs text-red-500">Proposta Rejeitada</p>
                                    <p className="font-semibold text-red-600">Análise de Crédito</p>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        )
                      })}
                    </div>
                  </div>
                )}

                {/* In Progress Section */}
                {inProgress.length > 0 && (
                  <div>
                    <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 shadow-xl rounded-2xl border-0 border-l-4 border-l-blue-500">
                      <CardHeader className="pb-3 lg:pb-4">
                        <CardTitle className="flex items-center gap-2 text-lg lg:text-xl text-blue-700">
                          <div className="p-2 bg-blue-100 rounded-full">
                            <Clock className="w-5 h-5 lg:w-6 lg:h-6 text-blue-600" />
                          </div>
                          Em Andamento ({inProgress.length})
                          <div className="ml-auto">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              Processando
                            </span>
                          </div>
                        </CardTitle>
                        <p className="text-sm lg:text-base text-gray-700">
                          Solicitações sendo processadas automaticamente
                        </p>
                      </CardHeader>
                    </Card>

                    <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 mt-4 lg:mt-6">
                      {inProgress.map((request) => {
                        const statusDisplay = getStatusDisplay(request.currentStatus)

                        return (
                          <Card
                            key={request.id}
                            className="bg-gradient-to-br from-white to-blue-50/30 shadow-xl rounded-2xl border-0 border-l-4 border-l-blue-400 hover:shadow-2xl hover:border-l-blue-500 transition-all duration-200 lg:h-full relative overflow-hidden"
                          >
                            {/* In Progress Indicator */}
                            <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-blue-400"></div>
                            <div className="absolute top-1 right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
                            <CardContent className="p-6 lg:p-8 h-full flex flex-col">
                              <div className="flex items-start justify-between mb-4 lg:mb-6">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-2">
                                    <div className={`p-2 lg:p-3 rounded-lg ${statusDisplay.bg}`}>
                                      <div className={statusDisplay.color}>{statusDisplay.icon}</div>
                                    </div>
                                    <div>
                                      <h3 className="font-semibold lg:text-lg text-gray-800">{request.statusLabel}</h3>
                                      <p className="text-xs lg:text-sm text-gray-500">ID: {request.id.slice(-8)}</p>
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-[#0B4375] hover:bg-blue-50 lg:h-10 lg:w-10"
                                >
                                  <Eye className="w-4 h-4" />
                                </Button>
                              </div>

                              <div className="grid grid-cols-2 gap-4 mb-4">
                                <div className="flex items-center gap-2">
                                  <DollarSign className="w-4 h-4 text-gray-400" />
                                  <div>
                                    <p className="text-xs text-gray-500">Valor Mensal</p>
                                    <p className="font-semibold text-gray-800">{formatCurrency(request.rentAmount)}</p>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Calendar className="w-4 h-4 text-gray-400" />
                                  <div>
                                    <p className="text-xs text-gray-500">Meses</p>
                                    <p className="font-semibold text-gray-800">{request.monthsToAdvance}</p>
                                  </div>
                                </div>

                              {request.realEstate && (
                                <div className="flex items-center gap-2 mb-4">
                                  <Building className="w-4 h-4 text-gray-400" />
                                  <div>
                                    <p className="text-xs text-gray-500">Imobiliária</p>
                                    <p className="text-sm text-gray-700">{request.realEstate.name}</p>
                                  </div>
                                </div>
                              )}

                              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                                <div className="flex items-center gap-2">
                                  <Clock className="w-4 h-4 text-gray-400" />
                                  <span className="text-xs text-gray-500">
                                    Atualizado em {formatDateTime(request.updatedAt)}
                                  </span>
                                </div>
                                {request.proposalAmount && request.proposalAmount !== -1 && (
                                  <div className="text-right">
                                    <p className="text-xs text-gray-500">Proposta</p>
                                    <p className="font-semibold text-green-600">{formatCurrency(request.proposalAmount)}</p>
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        )
                      })}
                    </div>
                  </div>
                )}
              </div>
            )
          })()
        )}
      </div>
    </div>
  )
}
