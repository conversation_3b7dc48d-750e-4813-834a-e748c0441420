"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { api } from "@/lib/api-handler"
import RentalAdvanceDetailsModal from "@/components/admin/RentalAdvanceDetailsModal"
import {
  Search,
  Filter,
  Calendar,
  DollarSign,
  Building,
  User,
  RefreshCw,
  AlertCircle,
  Download,
  Eye,
} from "lucide-react"

interface RequestItem {
  id: string
  rentAmount: number
  monthsToAdvance: number
  proposalAmount?: number
  currentStatus: string
  createdAt: string
  updatedAt: string
  user: {
    name: string
    cpf: string
    phone?: string
  }
  realEstate?: {
    name: string
    cnpj: string
  }
}

interface RequestsResponse {
  requests: RequestItem[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export default function AllRequestsPage() {
  const [requests, setRequests] = useState<RequestsResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [filters, setFilters] = useState({
    status: "all",
    startDate: "",
    endDate: "",
    realEstateId: "",
  })
  const [selectedOperationId, setSelectedOperationId] = useState<string | null>(null)
  const [modalOpen, setModalOpen] = useState(false)

  const fetchRequests = async (page: number = 1) => {
    setLoading(true)
    setError("")

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: "20",
      })

      // Add filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== "all") {
          params.append(key, value)
        }
      })

      const response = await api.adminGet<RequestsResponse>(`admin/requests?${params}`)
      
      if (!response.success) {
        throw new Error(response.error || "Failed to fetch requests")
      }

      setRequests(response.data!)
      setCurrentPage(page)
    } catch (error) {
      console.error("Error fetching requests:", error)
      setError("Erro ao carregar solicitações")
    } finally {
      setLoading(false)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const applyFilters = () => {
    setCurrentPage(1)
    fetchRequests(1)
  }

  const clearFilters = () => {
    setFilters({
      status: "all",
      startDate: "",
      endDate: "",
      realEstateId: "",
    })
    setCurrentPage(1)
    fetchRequests(1)
  }

  const exportData = async () => {
    try {
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value)
        }
      })

      const response = await api.adminGet(`admin/requests/export?${params}`)
      
      if (!response.success) {
        throw new Error(response.error || "Failed to export data")
      }

      // Create and download CSV
      const csvContent = convertToCSV(response.data.data)
      const blob = new Blob([csvContent], { type: 'text/csv' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `solicitacoes_${new Date().toISOString().split('T')[0]}.csv`
      a.click()
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error exporting data:", error)
      setError("Erro ao exportar dados")
    }
  }

  const convertToCSV = (data: any[]) => {
    if (!data.length) return ""
    
    const headers = Object.keys(data[0]).join(",")
    const rows = data.map(row => Object.values(row).join(","))
    return [headers, ...rows].join("\n")
  }

  useEffect(() => {
    fetchRequests()
  }, [])

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  const getStatusBadge = (status: string) => {
    const statusMap: Record<string, { label: string; variant: "default" | "secondary" | "destructive" | "outline" }> = {
      created: { label: "Criada", variant: "outline" },
      pdf_uploaded: { label: "PDF Enviado", variant: "outline" },
      pdf_extracted: { label: "Dados Extraídos", variant: "outline" },
      data_confirmed: { label: "Dados Confirmados", variant: "outline" },
      pending_proposal: { label: "Aguardando Proposta", variant: "secondary" },
      proposal_sent: { label: "Proposta Enviada", variant: "secondary" },
      docs_uploaded: { label: "Documentos Enviados", variant: "secondary" },
      awaiting_review: { label: "Em Análise", variant: "secondary" },
      approved: { label: "Aprovada", variant: "default" },
      rejected: { label: "Rejeitada", variant: "destructive" },
      cancelled: { label: "Cancelada", variant: "outline" },
    }

    const config = statusMap[status] || { label: status, variant: "outline" as const }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  const handleViewDetails = (operationId: string) => {
    setSelectedOperationId(operationId)
    setModalOpen(true)
  }

  if (loading && !requests) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Todas as Solicitações</h2>
          <Button disabled>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            Carregando...
          </Button>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-8 bg-gray-200 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-primary">Todas as Solicitações</h2>
          <p className="text-muted-foreground">
            {requests?.pagination.total || 0} solicitação{requests?.pagination.total !== 1 ? "ões" : ""} encontrada{requests?.pagination.total !== 1 ? "s" : ""}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={exportData} variant="outline" className="border-primary text-primary hover:bg-primary/10">
            <Download className="mr-2 h-4 w-4" />
            Exportar
          </Button>
          <Button onClick={() => fetchRequests(currentPage)} className="bg-primary hover:bg-primary/90">
            <RefreshCw className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-primary">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Status</label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange("status", value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos os status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os status</SelectItem>
                  <SelectItem value="created">Criada</SelectItem>
                  <SelectItem value="awaiting_review">Em Análise</SelectItem>
                  <SelectItem value="approved">Aprovada</SelectItem>
                  <SelectItem value="rejected">Rejeitada</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Data Início</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange("startDate", e.target.value)}
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Data Fim</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange("endDate", e.target.value)}
              />
            </div>
            
            <div className="flex items-end gap-2">
              <Button onClick={applyFilters} className="flex-1 bg-primary hover:bg-primary/90">
                <Search className="mr-2 h-4 w-4" />
                Filtrar
              </Button>
              <Button onClick={clearFilters} variant="outline" className="border-primary text-primary hover:bg-primary/10">
                Limpar
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Requests List */}
      {requests && (
        <>
          {requests.requests.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhuma solicitação encontrada</h3>
                <p className="text-gray-600">Tente ajustar os filtros ou verificar novamente mais tarde.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {requests.requests.map((request) => (
                <Card key={request.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{request.user.name}</h3>
                          <Badge variant="outline">ID: {request.id.slice(-8)}</Badge>
                          {getStatusBadge(request.currentStatus)}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">CPF:</span>
                            <span className="font-medium">{formatCPF(request.user.cpf)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Aluguel:</span>
                            <span className="font-medium">{formatCurrency(request.rentAmount)}</span>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Meses:</span>
                            <span className="font-medium">{request.monthsToAdvance}</span>
                          </div>
                          
                          {request.realEstate && (
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">Imobiliária:</span>
                              <span className="font-medium">{request.realEstate.name}</span>
                            </div>
                          )}
                          
                          {request.proposalAmount && (
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <span className="text-gray-600">Proposta:</span>
                              <span className="font-medium text-green-600">{formatCurrency(request.proposalAmount)}</span>
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span className="text-gray-600">Criado:</span>
                            <span className="font-medium">{formatDate(request.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(request.id)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Ver Detalhes
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Pagination */}
              {requests.pagination.totalPages > 1 && (
                <div className="flex items-center justify-center gap-2 pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchRequests(currentPage - 1)}
                    disabled={currentPage === 1 || loading}
                  >
                    Anterior
                  </Button>
                  
                  <span className="text-sm text-gray-600">
                    Página {currentPage} de {requests.pagination.totalPages}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fetchRequests(currentPage + 1)}
                    disabled={currentPage === requests.pagination.totalPages || loading}
                  >
                    Próxima
                  </Button>
                </div>
              )}
            </div>
          )}
        </>
      )}

      {/* Rental Advance Details Modal */}
      <RentalAdvanceDetailsModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        operationId={selectedOperationId}
      />
    </div>
  )
}
