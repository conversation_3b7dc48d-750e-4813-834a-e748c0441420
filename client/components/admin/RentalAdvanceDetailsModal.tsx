"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { rentalAdvanceApi } from "@/lib/api-handler"
import StatusTimeline from "./StatusTimeline"
import {
  User,
  FileText,
  DollarSign,
  Calendar,
  Building,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  Eye,
  Download,
  MapPin,
  CreditCard,
  Phone,
  Hash,
} from "lucide-react"

interface RentalAdvanceDetailsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  operationId: string | null
}

interface RentalAdvanceDetail {
  id: string
  rentAmount: number
  monthsToAdvance: number
  currentStatus: string
  statusLabel: string
  proposalAmount?: number
  monthlyRentOffer?: number
  proposedMonths?: number
  pixKey?: string
  user: {
    name: string
    cpf: string
    phone?: string
  }
  realEstate?: {
    id: string
    name: string
    cnpj: string
  }
  contractData?: {
    propertyAddress?: string
    landlordName?: string
    tenantName?: string
    landlordDocument?: string
    tenantDocument?: string
    rentalGuarantee?: string
    contractTerm?: string
    startDate?: string
    endDate?: string
    propertyRegistry?: string
  }
  documents: {
    contractPdf?: {
      url: string
      uploadedAt: string
    }
    identityDoc?: {
      url: string
      uploadedAt: string
    }
  }
  statusHistory: {
    status: string
    statusLabel: string
    createdAt: string
  }[]
  createdAt: string
  updatedAt: string
}

const RentalAdvanceDetailsModal: React.FC<RentalAdvanceDetailsModalProps> = ({
  open,
  onOpenChange,
  operationId,
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [data, setData] = useState<RentalAdvanceDetail | null>(null)

  const fetchDetails = async () => {
    if (!operationId) return

    setLoading(true)
    setError("")

    try {
      const response = await rentalAdvanceApi.getAdminRentalAdvanceDetails(operationId)
      
      if (!response.success) {
        throw new Error(response.error || "Erro ao carregar detalhes")
      }

      setData(response.data.data)
    } catch (error) {
      console.error("Error fetching rental advance details:", error)
      setError("Erro ao carregar detalhes da operação")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && operationId) {
      fetchDetails()
    }
  }, [open, operationId])

  const formatCurrency = (value: number) =>
    new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value)

  const formatCPF = (cpf: string) => {
    return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")
  }

  const formatCNPJ = (cnpj: string) => {
    return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, "$1.$2.$3/$4-$5")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("pt-BR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  const openFile = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  const getStatusBadge = (status: string, label: string) => {
    const statusColors: Record<string, string> = {
      approved: "bg-green-100 text-green-800 border-green-200",
      rejected: "bg-red-100 text-red-800 border-red-200",
      cancelled: "bg-gray-100 text-gray-800 border-gray-200",
      awaiting_review: "bg-blue-100 text-blue-800 border-blue-200",
      proposal_sent: "bg-purple-100 text-purple-800 border-purple-200",
    }
    
    const colorClass = statusColors[status] || "bg-gray-100 text-gray-800 border-gray-200"
    
    return (
      <Badge className={`${colorClass} border`}>
        {label}
      </Badge>
    )
  }

  if (!open) return null

  console.log(data)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-primary flex items-center gap-2">
            <FileText className="h-6 w-6" />
            Detalhes da Operação
            {data && (
              <Badge variant="outline" className="ml-2">
                ID: {data.id.slice(-8)}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        {loading && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600">Carregando detalhes...</span>
          </div>
        )}

        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-5 w-5" />
                <span>{error}</span>
              </div>
            </CardContent>
          </Card>
        )}

        {data && (
          <div className="space-y-6">
            {/* User Information Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-primary">
                  <User className="h-5 w-5" />
                  Informações do Usuário
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Nome Completo</label>
                    <p className="text-lg font-semibold text-gray-900">{data.user.name}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">CPF</label>
                    <p className="text-lg font-semibold text-gray-900">{formatCPF(data.user.cpf)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Telefone</label>
                    <p className="text-lg font-semibold text-gray-900">
                      {data.user.phone || "Não informado"}
                    </p>
                  </div>
                </div>
                
                <Separator className="my-4" />
                
                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status Atual</label>
                    <div className="mt-1">
                      {getStatusBadge(data.currentStatus, data.statusLabel)}
                    </div>
                  </div>
                  <div className="text-right">
                    <label className="text-sm font-medium text-gray-600">ID da Operação</label>
                    <p className="text-lg font-mono text-gray-900">{data.id}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Operation Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-primary">
                  <DollarSign className="h-5 w-5" />
                  Detalhes da Operação
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Valor do Aluguel</label>
                    <p className="text-xl font-bold text-green-600">{formatCurrency(data.rentAmount)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Meses Solicitados</label>
                    <p className="text-xl font-bold text-gray-900">{data.monthsToAdvance}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Criado em</label>
                    <p className="text-lg font-semibold text-gray-900">{formatDate(data.createdAt)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Última Atualização</label>
                    <p className="text-lg font-semibold text-gray-900">{formatDate(data.updatedAt)}</p>
                  </div>
                </div>
                
                {data.realEstate && (
                  <>
                    <Separator className="my-4" />
                    <div>
                      <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                        <Building className="h-4 w-4" />
                        Imobiliária Parceira
                      </label>
                      <div className="mt-2 flex items-center gap-4">
                        <p className="text-lg font-semibold text-gray-900">{data.realEstate.name}</p>
                        <Badge variant="outline">{formatCNPJ(data.realEstate.cnpj)}</Badge>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Status Timeline */}
              <StatusTimeline
                statusHistory={data.statusHistory}
                currentStatus={data.currentStatus}
              />

              {/* File Access Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-primary">
                    <FileText className="h-5 w-5" />
                    Arquivos
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {data.documents.contractPdf ? (
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-red-600" />
                        <div>
                          <p className="font-medium text-gray-900">Contrato PDF</p>
                          <p className="text-sm text-gray-600">
                            Enviado em {formatDate(data.documents.contractPdf.uploadedAt)}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openFile(data.documents.contractPdf!.url)}
                        className="border-primary text-primary hover:bg-primary/10"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Abrir
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                      <AlertCircle className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-600">Contrato PDF</p>
                        <p className="text-sm text-gray-500">Envio Pendente</p>
                      </div>
                    </div>
                  )}

                  {data.documents.identityDoc ? (
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <div>
                          <p className="font-medium text-gray-900">Documento de Identidade</p>
                          <p className="text-sm text-gray-600">
                            Enviado em {formatDate(data.documents.identityDoc.uploadedAt)}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openFile(data.documents.identityDoc!.url)}
                        className="border-primary text-primary hover:bg-primary/10"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Abrir
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                      <AlertCircle className="h-5 w-5 text-gray-400" />
                      <div>
                        <p className="font-medium text-gray-600">Documento de Identidade</p>
                        <p className="text-sm text-gray-500">Não enviado</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Contract PDF Data Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-primary">
                  <FileText className="h-5 w-5" />
                  Dados do Contrato
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.contractData ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          Endereço do Imóvel
                        </label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.propertyAddress || "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600">Nome do Locador</label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.landlordName || "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600">Documento do Locador</label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.landlordDocument || "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600">Nome do Locatário</label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.tenantName || "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600">Documento do Locatário</label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.tenantDocument || "Não informado"}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600">Garantia Locatícia</label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.rentalGuarantee || "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Data de Início
                        </label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.startDate ? formatDate(data.contractData.startDate) : "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Data de Término
                        </label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.endDate ? formatDate(data.contractData.endDate) : "Não informado"}
                        </p>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                          <Hash className="h-4 w-4" />
                          Matrícula do Imóvel
                        </label>
                        <p className="text-gray-900 font-medium">
                          {data.contractData.propertyRegistry || "Não informado"}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-600 mb-2">Envio Pendente</h3>
                    <p className="text-gray-500">
                      Os dados do contrato serão exibidos após o upload e processamento do PDF.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Proposal Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-primary">
                  <DollarSign className="h-5 w-5" />
                  Proposta de Crédito
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.proposalAmount && data.proposalAmount > 0 ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-green-50 border border-green-200 rounded-lg">
                        <label className="text-sm font-medium text-green-700">Valor da Proposta</label>
                        <p className="text-2xl font-bold text-green-800">
                          {formatCurrency(data.proposalAmount)}
                        </p>
                      </div>

                      {data.monthlyRentOffer && (
                        <div className="text-center p-4 bg-blue-50 border border-blue-200 rounded-lg">
                          <label className="text-sm font-medium text-blue-700">Oferta Mensal</label>
                          <p className="text-2xl font-bold text-blue-800">
                            {formatCurrency(data.monthlyRentOffer)}
                          </p>
                        </div>
                      )}

                      {data.proposedMonths && (
                        <div className="text-center p-4 bg-purple-50 border border-purple-200 rounded-lg">
                          <label className="text-sm font-medium text-purple-700">Meses Propostos</label>
                          <p className="text-2xl font-bold text-purple-800">
                            {data.proposedMonths}
                          </p>
                        </div>
                      )}
                    </div>

                    {data.pixKey && (
                      <div className="mt-4 p-4 bg-gray-50 border rounded-lg">
                        <label className="text-sm font-medium text-gray-600 flex items-center gap-1">
                          <CreditCard className="h-4 w-4" />
                          Chave PIX
                        </label>
                        <p className="text-lg font-mono text-gray-900 mt-1">{data.pixKey}</p>
                      </div>
                    )}

                    <div className="flex items-center gap-2 mt-4">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="text-green-700 font-medium">
                        Proposta gerada e disponível para o usuário
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    {['pending_proposal', 'processing_proposal'].includes(data.currentStatus) ? (
                      <>
                        <Clock className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-600 mb-2">Proposta em Geração</h3>
                        <p className="text-gray-500">
                          A proposta está sendo processada pelo sistema. Aguarde alguns minutos.
                        </p>
                      </>
                    ) : ['created', 'pdf_uploaded', 'processing_extraction', 'pdf_extracted', 'data_confirmed'].includes(data.currentStatus) ? (
                      <>
                        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-600 mb-2">Aguardando Etapas Anteriores</h3>
                        <p className="text-gray-500">
                          O usuário ainda não chegou na etapa de geração de proposta.
                        </p>
                      </>
                    ) : (
                      <>
                        <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
                        <h3 className="text-lg font-semibold text-gray-600 mb-2">Proposta Não Disponível</h3>
                        <p className="text-gray-500">
                          Nenhuma proposta foi gerada para esta operação.
                        </p>
                      </>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default RentalAdvanceDetailsModal
