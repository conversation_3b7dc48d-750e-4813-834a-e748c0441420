"use client"

import React from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  FileText, 
  Upload, 
  Eye, 
  Send,
  UserCheck,
  DollarSign
} from "lucide-react"

interface StatusHistoryItem {
  status: string
  statusLabel: string
  createdAt: string
}

interface StatusTimelineProps {
  statusHistory: StatusHistoryItem[]
  currentStatus: string
}

const StatusTimeline: React.FC<StatusTimelineProps> = ({ statusHistory, currentStatus }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'created':
        return <FileText className="h-4 w-4" />
      case 'pdf_uploaded':
      case 'processing_extraction':
        return <Upload className="h-4 w-4" />
      case 'pdf_extracted':
      case 'data_confirmed':
        return <Eye className="h-4 w-4" />
      case 'pending_proposal':
      case 'processing_proposal':
        return <Clock className="h-4 w-4" />
      case 'proposal_sent':
        return <Send className="h-4 w-4" />
      case 'docs_uploaded':
        return <Upload className="h-4 w-4" />
      case 'awaiting_review':
        return <UserCheck className="h-4 w-4" />
      case 'approved':
        return <CheckCircle className="h-4 w-4" />
      case 'rejected':
        return <XCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      case 'extraction_failed':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-600 bg-green-50 border-green-200'
      case 'rejected':
      case 'cancelled':
      case 'extraction_failed':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'awaiting_review':
      case 'proposal_sent':
        return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'processing_extraction':
      case 'processing_proposal':
      case 'pending_proposal':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return {
      date: date.toLocaleDateString("pt-BR", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      }),
      time: date.toLocaleTimeString("pt-BR", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
      })
    }
  }

  // Sort status history by date (newest first)
  const sortedHistory = [...statusHistory].sort((a, b) => 
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  )

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Clock className="h-5 w-5 text-primary" />
          Histórico de Status
        </h3>
        
        <div className="space-y-4">
          {sortedHistory.map((item, index) => {
            const { date, time } = formatDateTime(item.createdAt)
            const isCurrentStatus = item.status === currentStatus
            const colorClasses = getStatusColor(item.status)
            
            return (
              <div key={`${item.status}-${item.createdAt}`} className="flex items-start gap-4">
                {/* Timeline line */}
                <div className="flex flex-col items-center">
                  <div className={`p-2 rounded-full border-2 ${colorClasses}`}>
                    {getStatusIcon(item.status)}
                  </div>
                  {index < sortedHistory.length - 1 && (
                    <div className="w-px h-8 bg-gray-200 mt-2" />
                  )}
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge 
                      variant={isCurrentStatus ? "default" : "outline"}
                      className={isCurrentStatus ? "bg-primary text-primary-foreground" : ""}
                    >
                      {item.statusLabel}
                    </Badge>
                    {isCurrentStatus && (
                      <span className="text-xs text-primary font-medium">ATUAL</span>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    <div className="font-medium">{date}</div>
                    <div className="text-gray-500">{time}</div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {sortedHistory.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
            <p>Nenhum histórico de status disponível</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default StatusTimeline
