import {
  Injectable,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import { N8nService } from '../integrations/n8n/n8n.service';
import { DriveService } from '../integrations/drive/drive.service';
import { CacheService } from '../cache/cache.service';
import { CreateRentalAdvanceDto } from './dto/create-rental-advance.dto';
import { ConfirmExtractedDataDto } from './dto/confirm-extracted-data.dto';
import { ProposalRequestDto } from './dto/proposal-request.dto';
import { FinalConfirmationDto } from './dto/final-confirmation.dto';
import { ReviewDecisionDto } from './dto/review-decision.dto';
import { User } from '@prisma/client';
import { RentalAdvanceStatus } from './enums/rental-status.enum';
import { ValidationUtils } from '../common/utils/validation.utils';

@Injectable()
export class RentalAdvanceService {
  private readonly logger = new Logger(RentalAdvanceService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly n8nService: N8nService,
    private readonly driveService: DriveService,
    private readonly cacheService: CacheService,
  ) {}

  async listUserRequests(userId: string) {
    try {
      // Tentar recuperar do cache primeiro
      const cacheKey = this.cacheService.generateUserKey(userId, 'requests');
      const cachedData = await this.cacheService.get(cacheKey);
      if (cachedData) {
        this.logger.debug(`Cache hit para requests do usuário ${userId}`);
        return cachedData;
      }

      const data = await this.prisma.rentalAdvanceRequest.findMany({
        where: { userId },
        include: {
          realEstate: { select: { name: true, cnpj: true } },
          statusLogs: {
            orderBy: { createdAt: 'desc' },
            take: 3,
            select: { status: true, createdAt: true },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      // Armazenar no cache
      await this.cacheService.set(cacheKey, data, 300);
      return data;
    } catch (error) {
      this.logger.error(
        `Erro ao listar requisições do usuário ${userId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Erro ao buscar suas solicitações',
      );
    }
  }

  async createRequest(
    user: User,
    dto: CreateRentalAdvanceDto,
    contractPdf: Express.Multer.File,
  ) {
    // Validações de entrada
    if (dto.monthsToAdvance < 1 || dto.monthsToAdvance > 12) {
      throw new BadRequestException(
        'Meses para antecipação deve ser entre 1 e 12',
      );
    }

    if (dto.rentAmount <= 0) {
      throw new BadRequestException('Valor do aluguel deve ser positivo');
    }

    if (!contractPdf) {
      throw new BadRequestException('Arquivo PDF do contrato é obrigatório');
    }

    // Validação do arquivo
    ValidationUtils.validateFileType(contractPdf, ['application/pdf']);
    ValidationUtils.validateFileSize(contractPdf, 10); // 10MB max

    try {
      // Verifica se imobiliária existe
      const realEstate = await this.prisma.realEstate.findUnique({
        where: { id: dto.realEstateId },
      });

      if (!realEstate) {
        throw new BadRequestException('Imobiliária não encontrada');
      }

      this.logger.log(`Processando nova solicitação para usuário: ${user.id}`);

      // Criar registro inicial com status "created"
      const operation = await this.prisma.rentalAdvanceRequest.create({
        data: {
          userId: user.id,
          rentAmount: dto.rentAmount / 100, // No frontend, o valor vem sem centavos, então dividimos por 100
          monthsToAdvance: dto.monthsToAdvance,
          realEstateId: dto.realEstateId,
          currentStatus: RentalAdvanceStatus.CREATED,
        },
      });

      // Log inicial
      await this.addStatusLog(operation.id, RentalAdvanceStatus.CREATED);

      // Cria pasta no Drive e salva PDF em uma única operação para evitar duplicação
      const contractUrl = await this.driveService.saveContractPdf(
        operation.id,
        contractPdf,
      );

      // Atualiza com URL do contrato
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operation.id },
        data: {
          contractPdfUrl: contractUrl,
          currentStatus: RentalAdvanceStatus.PDF_UPLOADED,
        },
      });

      await this.addStatusLog(operation.id, RentalAdvanceStatus.PDF_UPLOADED);

      // Inicia extração via N8N (fire-and-forget)
      await this.n8nService.sendContractForExtraction(operation.id, contractPdf);

      // Retorna informações da operação criada (o frontend fará polling para verificar o status)
      return {
        operationId: operation.id,
        contractUrl,
        status: 'processing',
        message:
          'PDF enviado para análise. Os dados extraídos serão disponibilizados em breve.',
      };
    } catch (error) {
      this.logger.error(`Erro ao criar solicitação:`, error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao processar solicitação');
    }
  }

  async confirmExtractedData(user: User, dto: ConfirmExtractedDataDto) {
    try {
      // Busca a operação
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: dto.operationId,
          userId: user.id,
          currentStatus: RentalAdvanceStatus.PDF_EXTRACTED,
        },
      });

      if (!operation) {
        throw new NotFoundException(
          'Operação não encontrada ou não está no status correto',
        );
      }

      // Atualiza status
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operation.id },
        data: {
          currentStatus: RentalAdvanceStatus.DATA_CONFIRMED,
        },
      });

      await this.addStatusLog(operation.id, RentalAdvanceStatus.DATA_CONFIRMED);

      this.logger.log(`Dados confirmados para operação: ${operation.id}`);

      // Automaticamente inicia o processo de geração de proposta
      try {
        await this.requestProposal(user, { operationId: operation.id });
        this.logger.log(`Proposta solicitada automaticamente para operação: ${operation.id}`);
      } catch (error) {
        this.logger.error(`Erro ao solicitar proposta automaticamente: ${error.message}`);
        // Não falha a confirmação de dados se a proposta falhar
      }

      return { success: true, operationId: operation.id };
    } catch (error) {
      this.logger.error(`Erro ao confirmar dados extraídos:`, error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao confirmar dados');
    }
  }

  async requestProposal(user: User, dto: ProposalRequestDto) {
    try {
      // Busca a operação
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: dto.operationId,
          userId: user.id,
          currentStatus: RentalAdvanceStatus.DATA_CONFIRMED,
        },
        include: { contractData: true, realEstate: true },
      });

      if (!operation) {
        throw new NotFoundException(
          'Operação não encontrada ou não está no status correto',
        );
      }

      this.logger.log(`Solicitando proposta para operação: ${operation.id}`);

      // Atualiza status para "aguardando proposta"
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operation.id },
        data: { currentStatus: RentalAdvanceStatus.PENDING_PROPOSAL },
      });

      await this.addStatusLog(
        operation.id,
        RentalAdvanceStatus.PENDING_PROPOSAL,
      );

      // Inicia geração de proposta via N8N (fire-and-forget)
      this.logger.log(`Iniciando geração de proposta para operação: ${operation.id}`);
      await this.n8nService.requestProposal({
        operationId: operation.id,
        rentAmount: operation.rentAmount.toNumber(),
        monthsToAdvance: operation.monthsToAdvance,
        rentalGuarantee: operation.contractData?.rentalGuarantee,
        tenantDocument: operation.contractData?.tenantDocument,
        landlordDocument: operation.contractData?.landlordDocument,
        userId: user.id,        
      });

      this.logger.log(`Proposta solicitada para operação: ${operation.id}`);

      return {
        operationId: operation.id,
        status: 'pending_proposal',
        message: 'Proposta solicitada com sucesso. Aguarde a geração da proposta.',
      };
    } catch (error) {
      this.logger.error(`Erro ao solicitar proposta:`, error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao solicitar proposta');
    }
  }

  async finalConfirmation(
    user: User,
    dto: FinalConfirmationDto,
    identityDoc: Express.Multer.File,
  ) {
    if (!identityDoc) {
      throw new BadRequestException('Documento de identidade é obrigatório');
    }

    // Validação do arquivo
    ValidationUtils.validateFileType(identityDoc, [
      'image/jpeg',
      'image/png',
      'application/pdf',
    ]);
    ValidationUtils.validateFileSize(identityDoc, 5); // 5MB max

    // Validação da chave PIX
    if (!ValidationUtils.validatePixKey(dto.pixKey, dto.pixKeyType)) {
      throw new BadRequestException(
        `Chave PIX inválida para o tipo ${dto.pixKeyType}`,
      );
    }

    try {
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: dto.operationId,
          userId: user.id,
          currentStatus: RentalAdvanceStatus.PROPOSAL_SENT,
        },
      });

      if (!operation) {
        throw new NotFoundException(
          'Operação não encontrada ou não está no status correto',
        );
      }

      this.logger.log(
        `Processando confirmação final para operação: ${operation.id}`,
      );

      // Salva documento de identidade no Drive
      const identityDocUrl = await this.driveService.saveIdentityDoc(
        operation.id,
        identityDoc,
      );

      // Atualiza operação com dados finais
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operation.id },
        data: {
          pixKey: dto.pixKey,
          identityDocUrl,
          currentStatus: RentalAdvanceStatus.DOCS_UPLOADED,
        },
      });

      await this.addStatusLog(operation.id, RentalAdvanceStatus.DOCS_UPLOADED);

      // Marca automaticamente para revisão
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operation.id },
        data: { currentStatus: RentalAdvanceStatus.AWAITING_REVIEW },
      });

      await this.addStatusLog(
        operation.id,
        RentalAdvanceStatus.AWAITING_REVIEW,
      );

      this.logger.log(
        `Confirmação final concluída para operação: ${operation.id}`,
      );

      return {
        success: true,
        operationId: operation.id,
        status: RentalAdvanceStatus.AWAITING_REVIEW,
      };
    } catch (error) {
      this.logger.error(`Erro na confirmação final:`, error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro na confirmação final');
    }
  }

  async reviewDecision(reviewerUserId: string, dto: ReviewDecisionDto) {
    try {
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: dto.operationId },
        include: { user: { select: { name: true, cpf: true } } },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      if (operation.currentStatus !== RentalAdvanceStatus.AWAITING_REVIEW) {
        throw new BadRequestException('Operação não está aguardando revisão');
      }

      const newStatus =
        dto.decision === 'approved'
          ? RentalAdvanceStatus.APPROVED
          : RentalAdvanceStatus.REJECTED;

      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operation.id },
        data: { currentStatus: newStatus },
      });

      await this.addStatusLog(operation.id, newStatus, dto.reason);

      this.logger.log(
        `Operação ${operation.id} ${dto.decision} por ${reviewerUserId}`,
      );

      return {
        success: true,
        operationId: operation.id,
        status: newStatus,
        reason: dto.reason,
      };
    } catch (error) {
      this.logger.error(`Erro na decisão de revisão:`, error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new InternalServerErrorException('Erro na decisão de revisão');
    }
  }

  async getOperationsForReview(page: number = 1, limit: number = 20) {
    try {
      const skip = (page - 1) * limit;

      const [operations, total] = await Promise.all([
        this.prisma.rentalAdvanceRequest.findMany({
          where: { currentStatus: RentalAdvanceStatus.AWAITING_REVIEW },
          include: {
            user: { select: { name: true, cpf: true, phone: true } },
            realEstate: { select: { name: true, cnpj: true } },
            contractData: true,
            statusLogs: {
              orderBy: { createdAt: 'desc' },
              take: 5,
            },
          },
          orderBy: { createdAt: 'asc' },
          skip,
          take: limit,
        }),
        this.prisma.rentalAdvanceRequest.count({
          where: { currentStatus: RentalAdvanceStatus.AWAITING_REVIEW },
        }),
      ]);

      return {
        operations,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar operações para revisão:`, error);
      throw new InternalServerErrorException(
        'Erro ao buscar operações para revisão',
      );
    }
  }

  async getOperationById(operationId: string, userId?: string) {
    try {
      const where: any = { id: operationId };
      if (userId) {
        where.userId = userId;
      }

      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where,
        include: {
          user: { select: { name: true, cpf: true, phone: true } },
          realEstate: { select: { name: true, cnpj: true } },
          contractData: true,
          statusLogs: { orderBy: { createdAt: 'desc' } },
        },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      return operation;
    } catch (error) {
      this.logger.error(`Erro ao buscar operação ${operationId}:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar operação');
    }
  }

  /**
   * Busca dados extraídos do contrato
   */
  async getExtractedData(user: User, operationId: string) {
    try {
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: operationId,
          userId: user.id,
        },
        include: {
          contractData: true,
        },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      return {
        operationId: operation.id,
        status: operation.currentStatus,
        contractData: operation.contractData,
        contractUrl: operation.contractPdfUrl,
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar dados extraídos:`, error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar dados extraídos');
    }
  }

  /**
   * Deleta completamente uma operação de antecipação e limpa todos os dados relacionados
   */
  async deleteOperation(user: User, operationId: string) {
    try {
      this.logger.log(`Iniciando exclusão da operação: ${operationId} para usuário: ${user.id}`);

      // Verifica se a operação existe e pertence ao usuário
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: operationId,
          userId: user.id,
        },
        include: {
          contractData: true,
          statusLogs: true,
        },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      // Verifica se a operação está em um status que permite exclusão
      // Permitir exclusão apenas em status iniciais (antes da finalização)
      const allowedStatuses = [
        RentalAdvanceStatus.CREATED,
        RentalAdvanceStatus.PDF_UPLOADED,
        RentalAdvanceStatus.PROCESSING_EXTRACTION,
        RentalAdvanceStatus.PDF_EXTRACTED,
        RentalAdvanceStatus.EXTRACTION_FAILED,
      ];

      if (!allowedStatuses.includes(operation.currentStatus as RentalAdvanceStatus)) {
        throw new BadRequestException(
          'Esta operação não pode ser excluída no status atual'
        );
      }

      this.logger.log(`Operação encontrada, status: ${operation.currentStatus}`);

      // 1. Limpa arquivos do Google Drive (não falha se der erro)
      try {
        await this.driveService.deleteOperationFolder(operationId);
        this.logger.log(`Arquivos do Drive limpos para operação: ${operationId}`);
      } catch (error) {
        this.logger.error(`Erro ao limpar arquivos do Drive:`, error);
        // Continua com a exclusão mesmo se a limpeza do Drive falhar
      }

      // 2. Remove dados do banco de dados em transação
      await this.prisma.$transaction(async (tx) => {
        // Remove logs de status
        await tx.rentalRequestStatusLog.deleteMany({
          where: { rentalRequestId: operationId },
        });

        // Remove dados do contrato (se existir)
        if (operation.contractData) {
          await tx.rentalContractData.delete({
            where: { rentalRequestId: operationId },
          });
        }

        // Remove a operação principal
        await tx.rentalAdvanceRequest.delete({
          where: { id: operationId },
        });
      });

      // 3. Limpa cache relacionado
      const cacheKey = `user_operations_${user.id}`;
      await this.cacheService.del(cacheKey);

      this.logger.log(`Operação ${operationId} excluída com sucesso`);

      return {
        operationId,
        message: 'Operação excluída com sucesso',
      };
    } catch (error) {
      this.logger.error(`Erro ao excluir operação ${operationId}:`, error);

      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }

      throw new InternalServerErrorException('Erro ao excluir operação');
    }
  }

  private async addStatusLog(
    operationId: string,
    status: string,
    _notes?: string,
  ): Promise<void> {
    try {
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status,
        },
      });
    } catch (error) {
      this.logger.error(`Erro ao adicionar log de status:`, error);
      // Não lançar erro aqui para não quebrar o fluxo principal
    }
  }
}
